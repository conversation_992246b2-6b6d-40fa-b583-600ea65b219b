"""
时序违例插件性能测试脚本

用于测试和验证性能优化效果。
"""

import os
import sys
import time
import random
import psutil
from typing import List, Dict
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# 添加插件路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from parser import VioLogParser, HighPerformanceVioLogParser, AsyncVioLogParser, HighPerformanceAsyncParser
from performance_optimizer import PerformanceOptimizer


class PerformanceTester:
    """性能测试器"""
    
    def __init__(self):
        self.optimizer = PerformanceOptimizer()
        self.test_results = []
        
    def generate_test_data(self, output_file: str, violation_count: int):
        """生成测试数据文件
        
        Args:
            output_file: 输出文件路径
            violation_count: 违例数量
        """
        print(f"生成测试数据: {violation_count} 条违例记录...")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            for i in range(violation_count):
                # 生成随机违例数据
                num = i + 1
                hier = f"top.cpu.core{random.randint(0, 7)}.unit{random.randint(0, 15)}.reg{random.randint(0, 31)}"
                time_fs = random.randint(1000000, 10000000000)  # 1ns - 10s
                check_info = f"Setup time violation on signal clk_{random.randint(0, 3)}"
                
                # 写入违例记录
                f.write(f"NUM : {num}\n")
                f.write(f"Hier : {hier}\n")
                f.write(f"Time : {time_fs} FS\n")
                f.write(f"Check : {check_info}\n")
                f.write("------------------------------------------------------------\n")
        
        print(f"测试数据生成完成: {output_file}")
    
    def test_parser_performance(self, file_path: str) -> Dict:
        """测试解析器性能
        
        Args:
            file_path: 测试文件路径
            
        Returns:
            Dict: 测试结果
        """
        file_size = os.path.getsize(file_path)
        file_size_mb = file_size / (1024 * 1024)
        
        print(f"\n测试文件: {file_path}")
        print(f"文件大小: {file_size_mb:.2f} MB")
        
        results = {
            'file_path': file_path,
            'file_size_mb': file_size_mb,
            'parsers': {}
        }
        
        # 测试标准解析器
        print("\n1. 测试标准解析器...")
        try:
            start_time = time.time()
            start_memory = self._get_memory_usage()
            
            parser = VioLogParser()
            violations = parser.parse_log_file(file_path)
            
            end_time = time.time()
            end_memory = self._get_memory_usage()
            
            results['parsers']['standard'] = {
                'success': True,
                'parse_time': end_time - start_time,
                'violation_count': len(violations),
                'memory_usage': end_memory - start_memory,
                'throughput': len(violations) / (end_time - start_time)
            }
            
            print(f"  解析时间: {end_time - start_time:.2f}秒")
            print(f"  违例数量: {len(violations):,}")
            print(f"  内存使用: {end_memory - start_memory:.1f}MB")
            
        except Exception as e:
            print(f"  标准解析器失败: {str(e)}")
            results['parsers']['standard'] = {'success': False, 'error': str(e)}
        
        # 测试高性能解析器
        print("\n2. 测试高性能解析器...")
        try:
            start_time = time.time()
            start_memory = self._get_memory_usage()
            
            parser = HighPerformanceVioLogParser()
            violations = parser.parse_log_file_streaming(file_path)
            
            end_time = time.time()
            end_memory = self._get_memory_usage()
            
            results['parsers']['high_performance'] = {
                'success': True,
                'parse_time': end_time - start_time,
                'violation_count': len(violations),
                'memory_usage': end_memory - start_memory,
                'throughput': len(violations) / (end_time - start_time)
            }
            
            print(f"  解析时间: {end_time - start_time:.2f}秒")
            print(f"  违例数量: {len(violations):,}")
            print(f"  内存使用: {end_memory - start_memory:.1f}MB")
            
        except Exception as e:
            print(f"  高性能解析器失败: {str(e)}")
            results['parsers']['high_performance'] = {'success': False, 'error': str(e)}
        
        return results
    
    def test_ui_performance(self, violation_counts: List[int]) -> Dict:
        """测试UI性能
        
        Args:
            violation_counts: 测试的违例数量列表
            
        Returns:
            Dict: UI性能测试结果
        """
        print("\n=== UI性能测试 ===")
        
        # 需要QApplication实例
        if not QApplication.instance():
            app = QApplication(sys.argv)
        
        results = {
            'ui_tests': {}
        }
        
        for count in violation_counts:
            print(f"\n测试UI性能 - {count:,} 条记录...")
            
            # 生成测试数据
            test_violations = self._generate_test_violations(count)
            
            # 测试标准表格
            try:
                start_time = time.time()
                start_memory = self._get_memory_usage()
                
                # 模拟标准表格创建（不实际创建UI，只测试数据处理）
                self._simulate_standard_table_creation(test_violations)
                
                end_time = time.time()
                end_memory = self._get_memory_usage()
                
                results['ui_tests'][f'{count}_standard'] = {
                    'success': True,
                    'creation_time': end_time - start_time,
                    'memory_usage': end_memory - start_memory,
                    'record_count': count
                }
                
                print(f"  标准表格 - 创建时间: {end_time - start_time:.2f}秒, 内存: {end_memory - start_memory:.1f}MB")
                
            except Exception as e:
                print(f"  标准表格测试失败: {str(e)}")
                results['ui_tests'][f'{count}_standard'] = {'success': False, 'error': str(e)}
            
            # 测试高性能表格
            try:
                start_time = time.time()
                start_memory = self._get_memory_usage()
                
                # 模拟高性能表格创建
                self._simulate_high_performance_table_creation(test_violations)
                
                end_time = time.time()
                end_memory = self._get_memory_usage()
                
                results['ui_tests'][f'{count}_high_performance'] = {
                    'success': True,
                    'creation_time': end_time - start_time,
                    'memory_usage': end_memory - start_memory,
                    'record_count': count
                }
                
                print(f"  高性能表格 - 创建时间: {end_time - start_time:.2f}秒, 内存: {end_memory - start_memory:.1f}MB")
                
            except Exception as e:
                print(f"  高性能表格测试失败: {str(e)}")
                results['ui_tests'][f'{count}_high_performance'] = {'success': False, 'error': str(e)}
        
        return results
    
    def _get_memory_usage(self) -> float:
        """获取当前内存使用量（MB）"""
        try:
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except:
            return 0
    
    def _generate_test_violations(self, count: int) -> List[Dict]:
        """生成测试违例数据"""
        violations = []
        for i in range(count):
            violation = {
                'id': i + 1,
                'num': i + 1,
                'hier': f"top.cpu.core{i % 8}.unit{i % 16}.reg{i % 32}",
                'time_fs': random.randint(1000000, 10000000000),
                'check_info': f"Setup time violation on signal clk_{i % 4}",
                'status': 'pending',
                'confirmer': '',
                'result': ''
            }
            violations.append(violation)
        return violations
    
    def _simulate_standard_table_creation(self, violations: List[Dict]):
        """模拟标准表格创建过程"""
        # 模拟QTableWidgetItem创建开销
        for violation in violations:
            # 模拟创建8个单元格项目
            for col in range(8):
                item_data = str(violation.get('num', ''))
                # 模拟一些处理开销
                processed_data = item_data.upper().strip()
    
    def _simulate_high_performance_table_creation(self, violations: List[Dict]):
        """模拟高性能表格创建过程"""
        # 模拟分页处理
        page_size = 100
        total_pages = (len(violations) + page_size - 1) // page_size
        
        # 只处理第一页，模拟按需加载
        first_page_violations = violations[:page_size]
        
        for violation in first_page_violations:
            # 模拟轻量级控件创建
            for col in range(8):
                item_data = str(violation.get('num', ''))
                processed_data = item_data.strip()
    
    def run_comprehensive_test(self):
        """运行综合性能测试"""
        print("=== 时序违例插件性能测试 ===")
        print(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 测试配置
        test_configs = [
            {'violation_count': 1000, 'file_name': 'test_1k.log'},
            {'violation_count': 5000, 'file_name': 'test_5k.log'},
            {'violation_count': 10000, 'file_name': 'test_10k.log'},
            {'violation_count': 20000, 'file_name': 'test_20k.log'},
        ]
        
        # 创建测试目录
        test_dir = "performance_test_data"
        os.makedirs(test_dir, exist_ok=True)
        
        all_results = {
            'test_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'system_info': self._get_system_info(),
            'parser_tests': [],
            'ui_tests': {}
        }
        
        # 解析器性能测试
        print("\n=== 解析器性能测试 ===")
        for config in test_configs:
            file_path = os.path.join(test_dir, config['file_name'])
            
            # 生成测试数据
            if not os.path.exists(file_path):
                self.generate_test_data(file_path, config['violation_count'])
            
            # 测试解析性能
            result = self.test_parser_performance(file_path)
            all_results['parser_tests'].append(result)
        
        # UI性能测试
        ui_test_counts = [1000, 5000, 10000, 20000]
        ui_results = self.test_ui_performance(ui_test_counts)
        all_results['ui_tests'] = ui_results['ui_tests']
        
        # 生成测试报告
        self._generate_test_report(all_results)
        
        return all_results
    
    def _get_system_info(self) -> Dict:
        """获取系统信息"""
        try:
            return {
                'cpu_count': psutil.cpu_count(),
                'memory_total': psutil.virtual_memory().total / 1024 / 1024 / 1024,  # GB
                'python_version': sys.version,
                'platform': sys.platform
            }
        except:
            return {}
    
    def _generate_test_report(self, results: Dict):
        """生成测试报告"""
        report_file = f"performance_test_report_{int(time.time())}.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("时序违例插件性能测试报告\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"测试时间: {results['test_time']}\n")
            
            # 系统信息
            if results['system_info']:
                f.write(f"CPU核心数: {results['system_info'].get('cpu_count', 'N/A')}\n")
                f.write(f"总内存: {results['system_info'].get('memory_total', 'N/A'):.1f} GB\n")
                f.write(f"Python版本: {results['system_info'].get('python_version', 'N/A')}\n")
                f.write(f"平台: {results['system_info'].get('platform', 'N/A')}\n")
            
            f.write("\n解析器性能测试结果:\n")
            f.write("-" * 30 + "\n")
            
            for test in results['parser_tests']:
                f.write(f"\n文件大小: {test['file_size_mb']:.2f} MB\n")
                
                for parser_name, parser_result in test['parsers'].items():
                    if parser_result['success']:
                        f.write(f"  {parser_name}:\n")
                        f.write(f"    解析时间: {parser_result['parse_time']:.2f}秒\n")
                        f.write(f"    违例数量: {parser_result['violation_count']:,}\n")
                        f.write(f"    内存使用: {parser_result['memory_usage']:.1f}MB\n")
                        f.write(f"    吞吐量: {parser_result['throughput']:.0f} 记录/秒\n")
                    else:
                        f.write(f"  {parser_name}: 失败 - {parser_result.get('error', 'Unknown error')}\n")
            
            f.write("\nUI性能测试结果:\n")
            f.write("-" * 30 + "\n")
            
            for test_name, test_result in results['ui_tests'].items():
                if test_result['success']:
                    f.write(f"{test_name}:\n")
                    f.write(f"  创建时间: {test_result['creation_time']:.2f}秒\n")
                    f.write(f"  内存使用: {test_result['memory_usage']:.1f}MB\n")
                    f.write(f"  记录数量: {test_result['record_count']:,}\n")
                else:
                    f.write(f"{test_name}: 失败 - {test_result.get('error', 'Unknown error')}\n")
        
        print(f"\n测试报告已生成: {report_file}")


def main():
    """主函数"""
    tester = PerformanceTester()
    
    # 运行综合测试
    results = tester.run_comprehensive_test()
    
    print("\n=== 测试完成 ===")
    print("请查看生成的测试报告文件获取详细结果。")


if __name__ == "__main__":
    main()
