"""
简单的语法检查测试
"""

import ast
import sys
import os

def check_syntax(file_path):
    """检查Python文件语法"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            source = f.read()
        
        # 尝试解析AST
        ast.parse(source)
        print(f"✓ {file_path} 语法正确")
        return True
    except SyntaxError as e:
        print(f"✗ {file_path} 语法错误: {e}")
        print(f"  行号: {e.lineno}, 列号: {e.offset}")
        print(f"  错误文本: {e.text}")
        return False
    except Exception as e:
        print(f"✗ {file_path} 检查失败: {e}")
        return False

def main():
    print("=== 语法检查测试 ===")
    
    # 检查主要文件
    files_to_check = [
        'main_window.py',
        'parser.py', 
        'models.py',
        'performance_optimizer.py'
    ]
    
    all_good = True
    for file_name in files_to_check:
        if os.path.exists(file_name):
            if not check_syntax(file_name):
                all_good = False
        else:
            print(f"⚠ 文件不存在: {file_name}")
    
    if all_good:
        print("\n✓ 所有文件语法检查通过！")
    else:
        print("\n✗ 发现语法错误，请修复后重试")
    
    # 检查特定方法是否存在
    print("\n=== 方法检查 ===")
    try:
        with open('main_window.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        methods_to_check = [
            'def update_performance_display',
            'def _get_performance_suggestions',
            'def _get_memory_usage'
        ]
        
        for method in methods_to_check:
            if method in content:
                print(f"✓ 找到方法: {method}")
            else:
                print(f"✗ 缺少方法: {method}")
                
    except Exception as e:
        print(f"方法检查失败: {e}")

if __name__ == "__main__":
    main()
