#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
时序违例插件弹窗问题修复验证脚本

用于验证修复后的分页操作不再出现弹窗刷屏问题

修复验证要点：
1. ✅ Lambda闭包问题已修复 - 使用按钮属性存储数据
2. ✅ 安全消息框机制已实现 - 分页期间禁用QMessageBox
3. ✅ 分页操作已安全化 - 所有分页方法都有异常保护
4. ✅ 按钮创建已优化 - 添加数据验证和错误处理
5. ✅ 标准表格按钮也已修复 - 移除lambda函数
"""

import sys
import os
import time
import random

def test_safe_messagebox_mechanism():
    """测试安全消息框机制"""
    print("\n=== 测试安全消息框机制 ===")
    
    try:
        # 导入相关模块
        from main_window import (
            SafeMessageBox, enable_safe_messagebox, disable_safe_messagebox,
            _use_safe_messagebox
        )
        
        print("✓ 安全消息框模块导入成功")
        
        # 测试SafeMessageBox
        print("测试SafeMessageBox方法...")
        SafeMessageBox.warning(None, "测试警告", "这是一个测试警告")
        SafeMessageBox.critical(None, "测试错误", "这是一个测试错误")
        SafeMessageBox.information(None, "测试信息", "这是一个测试信息")
        result = SafeMessageBox.question(None, "测试询问", "这是一个测试询问")
        print(f"✓ SafeMessageBox测试完成，question返回值: {result}")
        
        # 测试开关机制
        print("测试安全消息框开关机制...")
        print(f"初始状态: {_use_safe_messagebox}")
        
        enable_safe_messagebox()
        print(f"启用后状态: {_use_safe_messagebox}")
        
        disable_safe_messagebox()
        print(f"禁用后状态: {_use_safe_messagebox}")
        
        print("✓ 安全消息框机制测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 安全消息框机制测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_button_creation_safety():
    """测试按钮创建安全性"""
    print("\n=== 测试按钮创建安全性 ===")
    
    try:
        from main_window import HighPerformanceTableView
        from PyQt5.QtWidgets import QApplication
        
        # 创建应用（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 创建高性能表格
        table = HighPerformanceTableView()
        
        # 测试按钮创建
        test_violation = {
            'id': 123,
            'status': 'pending',
            'num': 1,
            'hier': 'test.path',
            'time_fs': 1000000,
            'time_display': '1000 ps',
            'check_info': 'test violation'
        }
        
        print("测试正常按钮创建...")
        button = table.create_action_button(0, test_violation)
        if button:
            print("✓ 正常按钮创建成功")
            
            # 检查按钮属性
            violation_id = button.property('violation_id')
            action_type = button.property('action_type')
            row_index = button.property('row_index')
            
            print(f"  violation_id: {violation_id}")
            print(f"  action_type: {action_type}")
            print(f"  row_index: {row_index}")
            
            if violation_id == 123 and action_type == 'pending' and row_index == 0:
                print("✓ 按钮属性设置正确")
            else:
                print("✗ 按钮属性设置错误")
                return False
        else:
            print("✗ 按钮创建失败")
            return False
        
        # 测试异常情况
        print("测试异常数据按钮创建...")
        invalid_violation = {'status': 'pending'}  # 缺少id
        button = table.create_action_button(0, invalid_violation)
        if button and not button.isEnabled():
            print("✓ 异常数据处理正确，返回禁用的占位按钮")
        else:
            print("✗ 异常数据处理错误")
            return False
        
        print("✓ 按钮创建安全性测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 按钮创建安全性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pagination_safety():
    """测试分页操作安全性"""
    print("\n=== 测试分页操作安全性 ===")
    
    try:
        from main_window import HighPerformanceTableView
        from PyQt5.QtWidgets import QApplication
        
        # 创建应用（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 创建高性能表格
        table = HighPerformanceTableView()
        
        # 生成测试数据
        test_data = []
        for i in range(500):  # 生成500条数据，足够分页
            violation = {
                'id': i + 1,
                'status': 'pending',
                'num': i + 1,
                'hier': f'test.path.{i}',
                'time_fs': 1000000 + i,
                'time_display': f'{1000 + i} ps',
                'check_info': f'test violation {i}'
            }
            test_data.append(violation)
        
        print(f"生成 {len(test_data)} 条测试数据")
        
        # 更新数据
        print("测试数据更新...")
        table.update_data(test_data)
        print(f"✓ 数据更新成功，总页数: {table.total_pages}")
        
        # 测试分页操作
        print("测试分页操作...")
        
        # 测试下一页
        if table.total_pages > 1:
            original_page = table.current_page
            table.go_to_next_page()
            if table.current_page == original_page + 1:
                print("✓ 下一页操作成功")
            else:
                print("✗ 下一页操作失败")
                return False
        
        # 测试上一页
        if table.current_page > 0:
            original_page = table.current_page
            table.go_to_prev_page()
            if table.current_page == original_page - 1:
                print("✓ 上一页操作成功")
            else:
                print("✗ 上一页操作失败")
                return False
        
        # 测试首页
        table.go_to_first_page()
        if table.current_page == 0:
            print("✓ 首页操作成功")
        else:
            print("✗ 首页操作失败")
            return False
        
        # 测试末页
        table.go_to_last_page()
        if table.current_page == table.total_pages - 1:
            print("✓ 末页操作成功")
        else:
            print("✗ 末页操作失败")
            return False
        
        print("✓ 分页操作安全性测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 分页操作安全性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("时序违例插件弹窗问题修复验证")
    print("=" * 60)
    
    # 添加路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    sys.path.insert(0, current_dir)
    
    test_results = []
    
    # 执行各项测试
    test_results.append(("安全消息框机制", test_safe_messagebox_mechanism()))
    test_results.append(("按钮创建安全性", test_button_creation_safety()))
    test_results.append(("分页操作安全性", test_pagination_safety()))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！弹窗问题修复成功！")
        print("\n修复效果：")
        print("✅ 分页操作不再出现弹窗刷屏")
        print("✅ 错误信息改为控制台输出")
        print("✅ 按钮创建使用安全机制")
        print("✅ Lambda闭包问题已解决")
    else:
        print("❌ 部分测试失败，需要进一步检查修复")
    print("=" * 60)

if __name__ == "__main__":
    main()
