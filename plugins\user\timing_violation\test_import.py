"""
测试时序违例插件导入是否正常
"""

import sys
import os

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

try:
    print("开始测试插件导入...")
    
    # 测试主窗口导入
    from timing_violation.main_window import TimingViolationWindow
    print("✓ TimingViolationWindow 导入成功")
    
    # 测试解析器导入
    from timing_violation.parser import VioLogParser, HighPerformanceVioLogParser
    print("✓ 解析器导入成功")
    
    # 测试数据模型导入
    from timing_violation.models import ViolationDataModel
    print("✓ 数据模型导入成功")
    
    # 测试性能优化器导入
    try:
        from timing_violation.performance_optimizer import PerformanceOptimizer
        print("✓ 性能优化器导入成功")
    except ImportError as e:
        print(f"⚠ 性能优化器导入失败（可选组件）: {e}")
    
    # 测试方法是否存在
    window_class = TimingViolationWindow
    required_methods = [
        'update_performance_display',
        '_get_performance_suggestions',
        '_get_memory_usage'
    ]
    
    for method_name in required_methods:
        if hasattr(window_class, method_name):
            print(f"✓ 方法 {method_name} 存在")
        else:
            print(f"✗ 方法 {method_name} 不存在")
    
    print("\n=== 测试完成 ===")
    print("所有核心组件导入成功！")
    
except ImportError as e:
    print(f"✗ 导入失败: {e}")
    import traceback
    traceback.print_exc()
except Exception as e:
    print(f"✗ 测试失败: {e}")
    import traceback
    traceback.print_exc()
